import { DBOS } from '@dbos-inc/dbos-sdk';
import { ForaChat } from './operations';
import WebSocket from 'ws';

interface DelayedMessage {
  character: string;
  text: string;
  delay: number;
}

interface ChatResponse {
  reply: DelayedMessage[];
  theme?: string;
  skills?: string[];
  error?: string;
  details?: string;
  conversationId?: number;
}

interface StreamingSession {
  ws: WebSocket;
  isStreaming: boolean;
  timeouts: NodeJS.Timeout[];
  interrupted: boolean;
  pendingMessages: DelayedMessage[];
  conversationId?: number;
}

export class StreamingChatService {
  private sessions = new Map<string, StreamingSession>();

  createSession(sessionId: string, ws: WebSocket): void {
    const session: StreamingSession = {
      ws,
      isStreaming: false,
      timeouts: [],
      interrupted: false,
      pendingMessages: []
    };

    this.sessions.set(sessionId, session);

    ws.on('message', async (data) => {
      try {
        const message = JSON.parse(data.toString());
        await this.handleMessage(sessionId, message);
      } catch (error) {
        this.sendError(sessionId, 'Invalid message format');
      }
    });

    ws.on('close', () => {
      this.cleanupSession(sessionId);
    });

    // Send welcome message
    this.sendMessage(sessionId, {
      type: 'connected',
      sessionId
    });
  }

  private async handleMessage(sessionId: string, message: any): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    switch (message.type) {
      case 'chat':
        await this.handleChatMessage(sessionId, message.text);
        break;
      case 'interrupt':
        this.handleInterrupt(sessionId, message.text);
        break;
      default:
        this.sendError(sessionId, 'Unknown message type');
    }
  }

  private async handleChatMessage(sessionId: string, text: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    if (session.isStreaming) {
      // User interrupted while messages are being streamed
      this.handleInterrupt(sessionId, text);
      return;
    }

    try {
      // Start the chat workflow
      const handle = await DBOS.startWorkflow(ForaChat).chatWorkflow(text);
      const result: ChatResponse = await handle.getResult();

      if (result.error) {
        this.sendError(sessionId, result.error, result.details);
        return;
      }

      if (result.reply && Array.isArray(result.reply)) {
        // Store conversation ID for future interruptions
        session.conversationId = result.conversationId;

        this.sendMessage(sessionId, {
          type: 'chat_start',
          theme: result.theme || 'Chat Response'
        });

        await this.streamDelayedMessages(sessionId, result.reply, result.skills);
      } else {
        this.sendError(sessionId, 'Invalid response format from LLM');
      }
    } catch (error) {
      this.sendError(sessionId, 'Failed to process chat message', (error as Error).message);
    }
  }

  private async handleInterrupt(sessionId: string, newText: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    session.interrupted = true;
    this.clearTimeouts(sessionId);

    this.sendMessage(sessionId, {
      type: 'interrupted',
      message: 'Processing your new message...'
    });

    // Get the messages that were already sent
    const totalMessages = session.pendingMessages.length;
    const sentMessages: DelayedMessage[] = [];

    // Calculate which messages were already sent based on timing
    let cumulativeDelay = 0;
    const currentTime = Date.now();

    for (const message of session.pendingMessages) {
      cumulativeDelay += (message.delay || 0);
      // If enough time has passed for this message to be sent, include it
      if (cumulativeDelay <= 1000) { // Rough estimate - in real implementation would track start time
        sentMessages.push(message);
      } else {
        break;
      }
    }

    try {
      // Use the interrupted chat workflow with context
      const handle = await DBOS.startWorkflow(ForaChat).interruptedChatWorkflow(
        newText,
        sentMessages.map(msg => ({ character: msg.character, text: msg.text })),
        session.conversationId
      );
      const result: ChatResponse = await handle.getResult();

      if (result.error) {
        this.sendError(sessionId, result.error, result.details);
        return;
      }

      if (result.reply && Array.isArray(result.reply)) {
        this.sendMessage(sessionId, {
          type: 'chat_start',
          theme: result.theme || 'Chat Response (Interrupted)'
        });

        await this.streamDelayedMessages(sessionId, result.reply, result.skills);
      } else {
        this.sendError(sessionId, 'Invalid response format from LLM');
      }
    } catch (error) {
      this.sendError(sessionId, 'Failed to process interrupted message', (error as Error).message);
    }
  }

  private async streamDelayedMessages(
    sessionId: string, 
    messages: DelayedMessage[], 
    skills?: string[]
  ): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    session.isStreaming = true;
    session.interrupted = false;
    session.pendingMessages = [...messages];

    let cumulativeDelay = 0;

    messages.forEach((message, index) => {
      const delay = message.delay || 0;
      cumulativeDelay += delay; // Delay is already in milliseconds

      const timeout = setTimeout(() => {
        if (session.interrupted) {
          return; // Don't send if interrupted
        }

        this.sendMessage(sessionId, {
          type: 'message',
          character: message.character,
          text: message.text,
          index,
          total: messages.length
        });

        // If this is the last message, finish the stream
        if (index === messages.length - 1) {
          setTimeout(() => {
            if (!session.interrupted) {
              this.sendMessage(sessionId, {
                type: 'chat_complete',
                skills: skills || []
              });
              session.isStreaming = false;
              session.pendingMessages = [];
            }
          }, 100);
        }
      }, cumulativeDelay);

      session.timeouts.push(timeout);
    });
  }

  private clearTimeouts(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    session.timeouts.forEach(timeout => clearTimeout(timeout));
    session.timeouts = [];
  }

  private sendMessage(sessionId: string, message: any): void {
    const session = this.sessions.get(sessionId);
    if (!session || session.ws.readyState !== WebSocket.OPEN) return;

    session.ws.send(JSON.stringify(message));
  }

  private sendError(sessionId: string, error: string, details?: string): void {
    this.sendMessage(sessionId, {
      type: 'error',
      error,
      details
    });
  }

  private cleanupSession(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (session) {
      this.clearTimeouts(sessionId);
      this.sessions.delete(sessionId);
    }
  }

  getSessionCount(): number {
    return this.sessions.size;
  }
}
