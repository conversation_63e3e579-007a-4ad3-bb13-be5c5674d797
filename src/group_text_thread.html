<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Group Chat: Work Squad</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }
        
        .phone-container {
            width: 375px;
            height: 667px;
            background: #000;
            border-radius: 25px;
            padding: 8px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            position: relative;
        }

        .scroll-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .scroll-overlay.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .scroll-message {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            padding: 15px 25px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .screen {
            width: 100%;
            height: 100%;
            background: #f8f9fa;
            border-radius: 20px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .header {
            background: #007AFF;
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .back-arrow {
            font-size: 18px;
        }
        
        .group-info h3 {
            font-size: 16px;
            font-weight: 600;
        }
        
        .group-info p {
            font-size: 12px;
            opacity: 0.8;
        }
        
        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #fff;
        }
        
        .message {
            margin-bottom: 15px;
            max-width: 80%;
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .message.you {
            margin-left: auto;
        }
        
        .message.you .bubble {
            background: #007AFF;
            color: white;
            border-bottom-right-radius: 8px;
        }
        
        .message.fora .bubble,
        .message.jan .bubble,
        .message.lou .bubble {
            background: #E5E5EA;
            color: #000;
        }
        
        .message:not(.you) {
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }
        
        .avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #007AFF;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            flex-shrink: 0;
            margin-top: 18px;
        }
        
        .avatar.fora { background: #FF3B30; }
        .avatar.jan { background: #34C759; }
        .avatar.lou { background: #FF9500; }
        
        .message-content {
            flex: 1;
            max-width: calc(100% - 38px);
        }
        
        .message:not(.you) .bubble {
            border-bottom-left-radius: 8px;
        }
        
        .message.you .message-content {
            max-width: 80%;
        }
        
        .sender-name {
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 4px;
            color: #8E8E93;
        }
        
        .bubble {
            padding: 12px 16px;
            border-radius: 20px;
            font-size: 16px;
            line-height: 1.4;
            word-wrap: break-word;
        }
        
        .timestamp {
            font-size: 11px;
            color: #8E8E93;
            margin-top: 4px;
            text-align: center;
        }
        
        .message.you .timestamp {
            text-align: right;
        }
        
        .you .sender-name {
            display: none;
        }
        
        .message:not(.you) .timestamp {
            text-align: left;
        }
        
        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 15px;
            padding-left: 12px;
        }
        
        .typing-dots {
            background: #E5E5EA;
            border-radius: 20px;
            padding: 12px 16px;
            display: flex;
            gap: 4px;
        }
        
        .dot {
            width: 8px;
            height: 8px;
            background: #8E8E93;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }
        
        .dot:nth-child(2) { animation-delay: 0.2s; }
        .dot:nth-child(3) { animation-delay: 0.4s; }
        
        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }
        
        .input-area {
            padding: 15px 20px;
            background: #f8f9fa;
            border-top: 1px solid #e5e5ea;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .input-field {
            flex: 1;
            background: white;
            border: 1px solid #e5e5ea;
            border-radius: 20px;
            padding: 10px 15px;
            font-size: 16px;
            outline: none;
        }
        
        .send-btn {
            background: #007AFF;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="phone-container" data-phone-id="group-phone">
        <div class="scroll-overlay" id="overlay-group-phone">
            <div class="scroll-message">📱 Scroll to read</div>
        </div>
        <div class="screen">
            <div class="header">
                <div class="header-left">
                    <span class="back-arrow">‹</span>
                    <div class="group-info">
                        <h3>Work Squad</h3>
                        <p>Fora, Jan, Lou, You</p>
                    </div>
                </div>
                <div>📞 📹</div>
            </div>
            
            <div class="chat-container" id="chatContainer">
                <div class="message you">
                    <div class="message-content">
                        <div class="bubble">ughhhhh guys I cant anymore</div>
                        <div class="timestamp">2:14 PM</div>
                    </div>
                </div>
                
                <div class="message you">
                    <div class="message-content">
                        <div class="bubble">martin is literally the WORST</div>
                        <div class="timestamp">2:14 PM</div>
                    </div>
                </div>
                
                <div class="message fora">
                    <div class="avatar fora">F</div>
                    <div class="message-content">
                        <div class="sender-name">Fora</div>
                        <div class="bubble">oh no what did he do now??</div>
                        <div class="timestamp">2:15 PM</div>
                    </div>
                </div>
                
                <div class="message you">
                    <div class="message-content">
                        <div class="bubble">dude interrupts me EVERY time I talk in meetings</div>
                        <div class="timestamp">2:15 PM</div>
                    </div>
                </div>
                
                <div class="message you">
                    <div class="message-content">
                        <div class="bubble">and then acts like my ideas are his??? im so done</div>
                        <div class="timestamp">2:15 PM</div>
                    </div>
                </div>
                
                <div class="message jan">
                    <div class="avatar jan">J</div>
                    <div class="message-content">
                        <div class="sender-name">Jan</div>
                        <div class="bubble">ugh hate ppl like that</div>
                        <div class="timestamp">2:16 PM</div>
                    </div>
                </div>
                
                <div class="message lou">
                    <div class="avatar lou">L</div>
                    <div class="message-content">
                        <div class="sender-name">Lou</div>
                        <div class="bubble">wait is this the same guy who mansplained ur own project to u last month lol</div>
                        <div class="timestamp">2:16 PM</div>
                    </div>
                </div>
                
                <div class="message you">
                    <div class="message-content">
                        <div class="bubble">YES omg that guy 😤😤😤</div>
                        <div class="timestamp">2:16 PM</div>
                    </div>
                </div>
                
                <div class="message fora">
                    <div class="avatar fora">F</div>
                    <div class="message-content">
                        <div class="sender-name">Fora</div>
                        <div class="bubble">wait jan didnt u tell me about ur friend who dealt w/ something like this?</div>
                        <div class="timestamp">2:17 PM</div>
                    </div>
                </div>
                
                <div class="message jan">
                    <div class="avatar jan">J</div>
                    <div class="message-content">
                        <div class="sender-name">Jan</div>
                        <div class="bubble">omgggg yes sarah!!!</div>
                        <div class="timestamp">2:17 PM</div>
                    </div>
                </div>
                
                <div class="message jan">
                    <div class="avatar jan">J</div>
                    <div class="message-content">
                        <div class="sender-name">Jan</div>
                        <div class="bubble">she had this coworker david who was such a dick</div>
                        <div class="timestamp">2:17 PM</div>
                    </div>
                </div>
                
                <div class="message jan">
                    <div class="avatar jan">J</div>
                    <div class="message-content">
                        <div class="sender-name">Jan</div>
                        <div class="bubble">would literally take her presentations n present them as his own work 🙄</div>
                        <div class="timestamp">2:18 PM</div>
                    </div>
                </div>
                
                <div class="message you">
                    <div class="message-content">
                        <div class="bubble">THATS EXACTLY WHAT MARTIN DOES</div>
                        <div class="timestamp">2:18 PM</div>
                    </div>
                </div>
                
                <div class="message lou">
                    <div class="avatar lou">L</div>
                    <div class="message-content">
                        <div class="sender-name">Lou</div>
                        <div class="bubble">ok but what did she do tho?? bc this sounds awful</div>
                        <div class="timestamp">2:18 PM</div>
                    </div>
                </div>
                
                <div class="message jan">
                    <div class="avatar jan">J</div>
                    <div class="message-content">
                        <div class="sender-name">Jan</div>
                        <div class="bubble">so she tried talking to him first but he was like whatever</div>
                        <div class="timestamp">2:19 PM</div>
                    </div>
                </div>
                
                <div class="message jan">
                    <div class="avatar jan">J</div>
                    <div class="message-content">
                        <div class="sender-name">Jan</div>
                        <div class="bubble">then she got smart about it</div>
                        <div class="timestamp">2:19 PM</div>
                    </div>
                </div>
                
                <div class="message jan">
                    <div class="avatar jan">J</div>
                    <div class="message-content">
                        <div class="sender-name">Jan</div>
                        <div class="bubble">started writing down EVERYTHING. like every convo, every email, every idea she shared</div>
                        <div class="timestamp">2:20 PM</div>
                    </div>
                </div>
                
                <div class="message fora">
                    <div class="avatar fora">F</div>
                    <div class="message-content">
                        <div class="sender-name">Fora</div>
                        <div class="bubble">ooh smart</div>
                        <div class="timestamp">2:20 PM</div>
                    </div>
                </div>
                
                <div class="message lou">
                    <div class="avatar lou">L</div>
                    <div class="message-content">
                        <div class="sender-name">Lou</div>
                        <div class="bubble">yasss paper trail queen</div>
                        <div class="timestamp">2:20 PM</div>
                    </div>
                </div>
                
                <div class="message jan">
                    <div class="avatar jan">J</div>
                    <div class="message-content">
                        <div class="sender-name">Jan</div>
                        <div class="bubble">but here's the good part</div>
                        <div class="timestamp">2:21 PM</div>
                    </div>
                </div>
                
                <div class="message jan">
                    <div class="avatar jan">J</div>
                    <div class="message-content">
                        <div class="sender-name">Jan</div>
                        <div class="bubble">she started ccing her boss on stuff</div>
                        <div class="timestamp">2:21 PM</div>
                    </div>
                </div>
                
                <div class="message jan">
                    <div class="avatar jan">J</div>
                    <div class="message-content">
                        <div class="sender-name">Jan</div>
                        <div class="bubble">like "hey david here's the ideas we talked about for the johnson thing" cc: manager</div>
                        <div class="timestamp">2:21 PM</div>
                    </div>
                </div>
                
                <div class="message you">
                    <div class="message-content">
                        <div class="bubble">wait thats actually genius??</div>
                        <div class="timestamp">2:22 PM</div>
                    </div>
                </div>
                
                <div class="message fora">
                    <div class="avatar fora">F</div>
                    <div class="message-content">
                        <div class="sender-name">Fora</div>
                        <div class="bubble">right?? like casual but makes it obvs whose idea it was</div>
                        <div class="timestamp">2:22 PM</div>
                    </div>
                </div>
                
                <div class="message lou">
                    <div class="avatar lou">L</div>
                    <div class="message-content">
                        <div class="sender-name">Lou</div>
                        <div class="bubble">did it work??</div>
                        <div class="timestamp">2:22 PM</div>
                    </div>
                </div>
                
                <div class="message jan">
                    <div class="avatar jan">J</div>
                    <div class="message-content">
                        <div class="sender-name">Jan</div>
                        <div class="bubble">ok so next meeting david tries to present "his" ideas</div>
                        <div class="timestamp">2:23 PM</div>
                    </div>
                </div>
                
                <div class="message jan">
                    <div class="avatar jan">J</div>
                    <div class="message-content">
                        <div class="sender-name">Jan</div>
                        <div class="bubble">and sarah was like "oh thanks for expanding on what I sent yesterday david!"</div>
                        <div class="timestamp">2:23 PM</div>
                    </div>
                </div>
                
                <div class="message jan">
                    <div class="avatar jan">J</div>
                    <div class="message-content">
                        <div class="sender-name">Jan</div>
                        <div class="bubble">w her manager sitting RIGHT THERE who saw the email 👀</div>
                        <div class="timestamp">2:23 PM</div>
                    </div>
                </div>
                
                <div class="message lou">
                    <div class="avatar lou">L</div>
                    <div class="message-content">
                        <div class="sender-name">Lou</div>
                        <div class="bubble">STOP I love this woman</div>
                        <div class="timestamp">2:24 PM</div>
                    </div>
                </div>
                
                <div class="message fora">
                    <div class="avatar fora">F</div>
                    <div class="message-content">
                        <div class="sender-name">Fora</div>
                        <div class="bubble">not confrontational but still called him out perfectly</div>
                        <div class="timestamp">2:24 PM</div>
                    </div>
                </div>
                
                <div class="message you">
                    <div class="message-content">
                        <div class="bubble">omggg this is what I need to do w martin</div>
                        <div class="timestamp">2:25 PM</div>
                    </div>
                </div>
                
                <div class="message jan">
                    <div class="avatar jan">J</div>
                    <div class="message-content">
                        <div class="sender-name">Jan</div>
                        <div class="bubble">it totally worked! he stopped being such a credit stealer after that</div>
                        <div class="timestamp">2:25 PM</div>
                    </div>
                </div>
                
                <div class="message jan">
                    <div class="avatar jan">J</div>
                    <div class="message-content">
                        <div class="sender-name">Jan</div>
                        <div class="bubble">and her boss started noticing her work more</div>
                        <div class="timestamp">2:25 PM</div>
                    </div>
                </div>
                
                <div class="message you">
                    <div class="message-content">
                        <div class="bubble">ok im def gonna start documenting everything martin does</div>
                        <div class="timestamp">2:26 PM</div>
                    </div>
                </div>
                
                <div class="message you">
                    <div class="message-content">
                        <div class="bubble">and ccing our team lead on project stuff</div>
                        <div class="timestamp">2:26 PM</div>
                    </div>
                </div>
                
                <div class="message lou">
                    <div class="avatar lou">L</div>
                    <div class="message-content">
                        <div class="sender-name">Lou</div>
                        <div class="bubble">yes!! ur gonna handle this so well</div>
                        <div class="timestamp">2:27 PM</div>
                    </div>
                </div>
                
                <div class="message fora">
                    <div class="avatar fora">F</div>
                    <div class="message-content">
                        <div class="sender-name">Fora</div>
                        <div class="bubble">professional queen energy 💅</div>
                        <div class="timestamp">2:27 PM</div>
                    </div>
                </div>
                
                <div class="message you">
                    <div class="message-content">
                        <div class="bubble">honestly feeling so much better about this now</div>
                        <div class="timestamp">2:27 PM</div>
                    </div>
                </div>
                
                <div class="message you">
                    <div class="message-content">
                        <div class="bubble">thank u guys!! sarah sounds like a badass lol</div>
                        <div class="timestamp">2:28 PM</div>
                    </div>
                </div>
                
                <div class="message jan">
                    <div class="avatar jan">J</div>
                    <div class="message-content">
                        <div class="sender-name">Jan</div>
                        <div class="bubble">she is!! and ur gonna be too 💪</div>
                        <div class="timestamp">2:28 PM</div>
                    </div>
                </div>
                
                <div class="message jan">
                    <div class="avatar jan">J</div>
                    <div class="message-content">
                        <div class="sender-name">Jan</div>
                        <div class="bubble">keep us posted on how it goes w martin!</div>
                        <div class="timestamp">2:28 PM</div>
                    </div>
                </div>
            </div>
            
            <div class="input-area">
                <input type="text" class="input-field" placeholder="Message">
                <button class="send-btn">➤</button>
            </div>
        </div>
    </div>
    
    <script>
        // Start at the top instead of auto-scrolling to bottom
        const chatContainer = document.getElementById('chatContainer');
        chatContainer.scrollTop = 0;

        // Track scroll state for overlay
        let hasScrolled = false;
        const overlay = document.getElementById('overlay-group-phone');

        // Add scroll event listener for overlay management
        chatContainer.addEventListener('scroll', () => {
            const scrollTop = chatContainer.scrollTop;
            const scrollHeight = chatContainer.scrollHeight;
            const clientHeight = chatContainer.clientHeight;

            // Check if user has scrolled significantly (more than 50px or reached bottom)
            const scrolledSignificantly = scrollTop > 50 || (scrollTop + clientHeight >= scrollHeight - 10);

            if (scrolledSignificantly && !hasScrolled) {
                hasScrolled = true;
                overlay.classList.add('hidden');
            }
        });

        // Add some interactivity to the input
        const input = document.querySelector('.input-field');
        const sendBtn = document.querySelector('.send-btn');

        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                // In a real app, this would send the message
                input.value = '';
            }
        });

        sendBtn.addEventListener('click', function() {
            // In a real app, this would send the message
            input.value = '';
        });
    </script>
</body>
</html>